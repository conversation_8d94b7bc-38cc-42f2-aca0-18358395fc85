# 🔐 macOS 权限设置说明

## ⚠️ 遇到的问题
```
This process is not trusted! Input event monitoring will not be possible until it is added to accessibility clients.
```

这是 macOS 的安全机制，需要给程序授予"辅助功能"权限才能监听键盘输入。

## 🛠️ 解决方法

### 方法一：通过系统设置授权（推荐）

1. **打开系统设置**
   - 点击苹果菜单 → 系统设置（或系统偏好设置）

2. **进入隐私与安全性**
   - 选择"隐私与安全性"

3. **找到辅助功能**
   - 在左侧列表中找到"辅助功能"

4. **添加终端应用**
   - 点击"+"号添加应用
   - 找到并选择"终端"（Terminal）应用
   - 或者选择你正在使用的终端应用（如 iTerm2）

5. **确保开关打开**
   - 确保终端应用旁边的开关是打开状态

### 方法二：通过命令行授权

```bash
# 查看当前权限状态
sqlite3 /Library/Application\ Support/com.apple.TCC/TCC.db "SELECT * FROM access WHERE service='kTCCServiceAccessibility';"

# 注意：直接修改 TCC 数据库需要关闭 SIP，不推荐
```

## 🎮 授权完成后

重新运行程序：
```bash
python main_cli.py
```

现在应该可以正常监听键盘并播放语音了！

## 🔊 测试语音

按下以下按键测试：
- 数字键：1, 2, 3, 4, 5, 6, 7, 8, 9, 0
- 字母键：Q, W, E, R, T, Y, U, I, O, P

每个按键都会播放不同的搞笑语音！

## 🛑 退出程序

- 按 ESC 键
- 或者按 Ctrl+C

## 📝 注意事项

1. **权限是一次性设置**：设置完成后，以后运行程序都不需要重新设置
2. **安全考虑**：只给信任的应用授予辅助功能权限
3. **系统版本**：不同 macOS 版本的设置界面可能略有不同

## 🎉 享受游戏

设置完权限后，你就可以享受这个搞笑的按键语音玩具了！

每次按键都会：
- 显示按键信息
- 播放搞笑语音
- 显示有趣的表情符号
- 统计按键次数

这是一个很好的解压工具！🎮✨
