#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搞笑按键语音玩具 - 命令行版本
使用系统语音，更稳定可靠
"""

import threading
import subprocess
import sys
import time
from pynput import keyboard
import random

class FunnyKeyboardToy:
    def __init__(self):
        # 搞笑语音内容映射
        self.funny_sounds = {
            '1': "回答我！为什么不说话？",
            '2': "看着我的眼睛！不要逃避！",
            '3': "告诉我为什么？我很好奇！",
            '4': "你在干什么？快说实话！",
            '5': "不可能！我不相信！",
            '6': "哈哈哈哈！太搞笑了！",
            '7': "我不信！你在骗我！",
            '8': "真的吗？确定不是开玩笑？",
            '9': "太离谱了！怎么可能！",
            '0': "好吧好吧，我投降了！",
            'q': "什么鬼？这是什么操作？",
            'w': "我的天哪！吓死我了！",
            'e': "额...这个...怎么说呢...",
            'r': "让我想想...嗯...不对！",
            't': "太好玩了！再来一次！",
            'y': "耶！成功了！",
            'u': "呃...你确定吗？",
            'i': "我觉得不对劲...",
            'o': "哦！原来如此！",
            'p': "噗！忍不住笑了！"
        }
        
        self.key_count = 0
        self.running = True
        
        print("✅ 使用系统语音引擎 (macOS say 命令)")
        print("🎯 支持的按键：数字键 0-9, 字母键 Q-P")
        print("🛑 按 ESC 键退出程序")
        print("=" * 50)
        
        # 启动键盘监听
        self.start_keyboard_listener()
        
    def start_keyboard_listener(self):
        """启动键盘监听器"""
        def on_press(key):
            try:
                # 检查是否按下 ESC 键退出
                if key == keyboard.Key.esc:
                    print("\n👋 程序退出")
                    self.running = False
                    return False
                
                # 获取按键字符
                key_char = key.char.lower() if hasattr(key, 'char') and key.char else None
                
                if key_char and key_char in self.funny_sounds:
                    self.play_funny_sound(key_char)
                    
            except AttributeError:
                # 特殊按键（如方向键等）
                pass
        
        # 启动监听器
        self.listener = keyboard.Listener(on_press=on_press)
        self.listener.start()
        
    def play_funny_sound(self, key_char):
        """播放搞笑语音"""
        sound_text = self.funny_sounds[key_char]
        self.key_count += 1
        
        # 显示信息
        print(f"🎮 按键: {key_char.upper()} | 🔊 {sound_text} | 次数: {self.key_count}")
        
        # 在新线程中播放语音，避免阻塞
        def speak():
            try:
                # 使用 macOS 系统语音
                subprocess.run(["say", sound_text], check=True)
            except subprocess.CalledProcessError:
                print(f"❌ 语音播放失败: {sound_text}")
            except FileNotFoundError:
                print("❌ 系统语音命令不可用，请确保在 macOS 系统上运行")
        
        threading.Thread(target=speak, daemon=True).start()
        
        # 添加一些视觉效果
        self.add_visual_effect()
        
    def add_visual_effect(self):
        """添加视觉效果"""
        # 随机显示一些表情符号
        emojis = ['🎉', '😄', '🤣', '😂', '🎊', '✨', '🌟', '💫']
        print(f"    {random.choice(emojis)} 搞笑时刻！ {random.choice(emojis)}")
        
    def run(self):
        """运行应用"""
        try:
            # 保持程序运行
            while self.running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n👋 程序被中断退出")
        finally:
            if hasattr(self, 'listener'):
                self.listener.stop()

if __name__ == "__main__":
    print("🎮 搞笑按键语音玩具 - 命令行版本")
    print("=" * 50)
    print("按下数字键 0-9 或字母键 Q-P 来听搞笑语音！")
    print("按 ESC 键或 Ctrl+C 退出程序")
    print("=" * 50)
    
    app = FunnyKeyboardToy()
    app.run()
