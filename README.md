# 🎮 搞笑按键语音玩具 - LookMyEyes 解压神器

一个有趣的解压小工具，按下键盘按键时播放搞笑的中文语音，帮助你放松心情！

## ✨ 功能特点

- 🎯 **按键检测**: 监听键盘按键（数字键0-9，字母键Q-P）
- 🔊 **搞笑语音**: 每个按键对应不同的搞笑中文语音
- 🎨 **炫酷界面**: 简洁美观的图形界面，带有视觉效果
- 📊 **统计功能**: 显示按键次数统计
- 🌈 **视觉反馈**: 按键时有颜色闪烁效果

## 🚀 快速开始

### 方法一：使用启动器（推荐）
```bash
python start.py
```

### 方法二：手动安装
1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
python main.py
```

## 🎮 使用说明

1. 启动程序后会出现一个图形界面
2. 按下以下按键来听搞笑语音：
   - **数字键 0-9**: 各种搞笑回应
   - **字母键 Q-P**: 更多有趣语音

### 按键对应语音示例：
- `1` → "回答我！为什么不说话？"
- `2` → "看着我的眼睛！不要逃避！"
- `3` → "告诉我为什么？我很好奇！"
- `Q` → "什么鬼？这是什么操作？"
- `W` → "我的天哪！吓死我了！"
- ...更多搞笑语音等你发现！

## 🛠️ 技术特性

- **跨平台**: 支持 Windows、macOS、Linux
- **全局监听**: 无需程序获得焦点即可响应按键
- **多线程**: 语音播放不会阻塞界面
- **中文语音**: 自动检测并使用中文语音引擎

## 📋 系统要求

- Python 3.6+
- 支持的操作系统：Windows/macOS/Linux
- 音频输出设备

## 🔧 依赖包

- `pynput`: 全局键盘监听
- `pyttsx3`: 文本转语音
- `tkinter`: 图形界面（Python内置）

## 🎯 自定义语音

你可以编辑 `main.py` 文件中的 `funny_sounds` 字典来自定义按键对应的语音内容：

```python
self.funny_sounds = {
    '1': "你的自定义语音内容",
    '2': "另一个搞笑语音",
    # 添加更多...
}
```

## 🐛 常见问题

### Q: 没有声音？
A: 请检查：
- 系统音量是否开启
- 是否安装了语音引擎
- 尝试重启程序

### Q: 按键没有反应？
A: 请确保：
- 程序正在运行
- 按下的是支持的按键（0-9, Q-P）
- 程序有足够的权限

### Q: 语音不是中文？
A: 程序会自动检测中文语音引擎，如果没有中文语音，会使用系统默认语音。

## 🎉 享受游戏

现在开始按键盘，享受搞笑的语音反馈吧！这是一个很好的解压工具，特别适合：
- 工作间隙放松
- 逗朋友开心
- 测试键盘手感
- 纯粹的娱乐

## 📝 许可证

本项目仅供学习和娱乐使用。

---

**祝你玩得开心！🎮✨**
