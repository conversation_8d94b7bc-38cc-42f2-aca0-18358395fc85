#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搞笑按键语音玩具启动器
"""

import subprocess
import sys
import os

def install_requirements():
    """安装依赖包"""
    print("🔧 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def main():
    print("🎮 欢迎使用搞笑按键语音玩具！")
    print("=" * 50)
    
    # 检查依赖
    try:
        import pynput
        import pyttsx3
        import tkinter
        print("✅ 所有依赖已安装")
    except ImportError as e:
        print(f"⚠️  缺少依赖: {e}")
        if input("是否自动安装依赖包？(y/n): ").lower() == 'y':
            if not install_requirements():
                print("❌ 安装失败，请手动运行: pip install -r requirements.txt")
                return
        else:
            print("请手动安装依赖包: pip install -r requirements.txt")
            return
    
    # 启动主程序
    print("🚀 启动程序...")
    try:
        from main import FunnyKeyboardToy
        app = FunnyKeyboardToy()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
