Metadata-Version: 2.1
Name: pyttsx3
Version: 2.90
Summary: Text to Speech (TTS) library for Python 2 and 3. Works without internet connection or delay. Supports multiple TTS engines, including Sapi5, nsss, and espeak.
Home-page: https://github.com/nateshmbhat/pyttsx3
Author: <PERSON><PERSON>hat
Author-email: nates<PERSON><PERSON><EMAIL>
License: UNKNOWN
Keywords: pyttsx,ivona,pyttsx for python3,TTS for python3,pyttsx3,text to speech for python,tts,text to speech,speech,speech synthesis,offline text to speech,offline tts,gtts
Platform: UNKNOWN
Classifier: Intended Audience :: End Users/Desktop
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Requires-Dist: pyobjc (>=2.4) ; platform_system == "Darwin"
Requires-Dist: comtypes ; platform_system == "Windows"
Requires-Dist: pypiwin32 ; platform_system == "Windows"
Requires-Dist: pywin32 ; platform_system == "Windows"

*****************************************************
pyttsx3 (offline TTS for Python 3)
*****************************************************

``pyttsx3`` is a text-to-speech conversion library in Python. Unlike alternative libraries, it works offline, and is compatible with both Python 2 and 3.

Installation
************
::

	pip install pyttsx3


If you recieve errors such as ``No module named win32com.client``, ``No module named win32``, or ``No module named win32api``, you will need to additionally install ``pypiwin32``.


Usage :
************
::

	import pyttsx3
	engine = pyttsx3.init()
	engine.say("I will speak this text")
	engine.runAndWait()


**Changing Voice , Rate and Volume :**

::

	import pyttsx3
	engine = pyttsx3.init() # object creation

	""" RATE"""
	rate = engine.getProperty('rate')   # getting details of current speaking rate
	print (rate)                        #printing current voice rate
	engine.setProperty('rate', 125)     # setting up new voice rate


	"""VOLUME"""
	volume = engine.getProperty('volume')   #getting to know current volume level (min=0 and max=1)
	print (volume)                          #printing current volume level
	engine.setProperty('volume',1.0)    # setting up volume level  between 0 and 1

	"""VOICE"""
	voices = engine.getProperty('voices')       #getting details of current voice
	#engine.setProperty('voice', voices[0].id)  #changing index, changes voices. o for male
	engine.setProperty('voice', voices[1].id)   #changing index, changes voices. 1 for female

	engine.say("Hello World!")
	engine.say('My current speaking rate is ' + str(rate))
	engine.runAndWait()
	engine.stop()

	"""Saving Voice to a file"""
	# On linux make sure that 'espeak' and 'ffmpeg' are installed
	engine.save_to_file('Hello World', 'test.mp3')
	engine.runAndWait()


**Full documentation of the Library**
#####################################

https://pyttsx3.readthedocs.io/en/latest/


Included TTS engines:
*********************
* sapi5
* nsss
* espeak

Feel free to wrap another text-to-speech engine for use with ``pyttsx3``.

Project Links:
**************

* PyPI (https://pypi.python.org)
* GitHub (https://github.com/nateshmbhat/pyttsx3)
* Full Documentation (https://pyttsx3.readthedocs.org)


